<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Login Page</title>
  <link rel="stylesheet" href="styles.css" />
</head>

<body>
  <header>
    <h2 class="logo">Login App</h2>
  </header>

  <div class="main">
    <h1>Login</h1>
    <h3>Enter your credentials</h3>

    <form id="loginForm">
      <label for="email">Email:</label>
      <input type="email" id="email" name="email" placeholder="Enter your Email" required />

      <label for="password">Password:</label>
      <input type="password" id="password" name="password" placeholder="Enter your Password" required />

      <div class="wrap">
        <button type="submit">Login</button>
      </div>
    </form>

    <p>Not registered? <a href="#">Create an account</a></p>
  </div>

  <script>
    // Hardcoded credentials for demo
    const validEmail = "<EMAIL>";
    const validPassword = "<EMAIL>";

    const form = document.getElementById("loginForm");
    form.addEventListener("submit", function (e) {
      e.preventDefault();

      const email = document.getElementById("email").value.trim();
      const password = document.getElementById("password").value.trim();

      if (email === validEmail && password === validPassword) {
        window.location.href = "home.html"; // Redirect to home page
      } else {
        alert("Invalid email or password. Try again.");
      }
    });
  </script>
</body>

</html>
