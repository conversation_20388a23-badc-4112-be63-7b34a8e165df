/* Global layout styles */
body {
    font-family: sans-serif;
    line-height: 1.5;
    min-height: 100vh;
    background: #f3f3f3;
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 70px;
}

/* Header */
header {
    background-color: #4caf50;
    color: white;
    padding: 15px 20px;
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    text-align: center;
    z-index: 1000;
}

.logo {
    margin: 0;
    font-size: 24px;
}

/* Container */
.main {
    background-color: #fff;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
    padding: 30px;
    width: 90%;
    max-width: 500px;
    text-align: center;
}

/* Headings */
h1 {
    color: #4caf50;
    margin: 0.5em 0 0.2em 0;
}

h3 {
    margin: 0 0 1em 0;
    color: #333;
    font-weight: normal;
}

/* Labels and Inputs */
label {
    display: block;
    width: 100%;
    margin-top: 10px;
    margin-bottom: 5px;
    text-align: left;
    color: #555;
    font-weight: bold;
}

input {
    display: block;
    width: 100%;
    margin-bottom: 15px;
    padding: 10px;
    box-sizing: border-box;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
}

/* Submit button */
.wrap {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

button {
    padding: 15px;
    border-radius: 10px;
    margin-top: 15px;
    margin-bottom: 15px;
    border: none;
    color: white;
    cursor: pointer;
    background-color: #4caf50;
    width: 100%;
    font-size: 16px;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: #45a049;
}

button:focus {
    outline: 2px solid #2e7d32;
    outline-offset: 2px;
}

/* Link */
p {
    font-size: 14px;
    color: #555;
}

p a {
    text-decoration: none;
    color: #4caf50;
}

p a:hover {
    text-decoration: underline;
}
